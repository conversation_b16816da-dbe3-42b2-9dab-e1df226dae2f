// SPDX-License-Identifier: BUSL-1.1

/*
    This file is part of the Onyx Protocol.

    (c) Enzyme Foundation <<EMAIL>>

    For the full license information, please view the LICENSE
    file that was distributed with this source code.
*/

pragma solidity ^0.8.0;

/// @title ISharesTransferValidator Interface
/// <AUTHOR> Foundation <<EMAIL>>
interface ISharesTransferValidator {
    function validateSharesTransfer(address _from, address _to, uint256 _amount) external;
}
